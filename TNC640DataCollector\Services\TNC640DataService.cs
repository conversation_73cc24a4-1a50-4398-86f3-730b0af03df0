using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TNC640DataCollector.Configuration;
using TNC640DataCollector.Interfaces;
using TNC640DataCollector.Models;

namespace TNC640DataCollector.Services
{
    /// <summary>
    /// TNC 640数据服务实现
    /// </summary>
    public class TNC640DataService : ITNC640DataService, IDisposable
    {
        private readonly ILogger<TNC640DataService> _logger;
        private readonly ITNC640CommunicationService _communicationService;
        private readonly TNC640Configuration _configuration;
        private TNC640Data _latestData;
        private readonly List<TNC640Data> _historicalData;
        private readonly object _dataLock = new object();
        private bool _isRunning;

        public event EventHandler<TNC640Data>? DataUpdated;
        public event EventHandler<bool>? ConnectionStatusChanged;

        public TNC640DataService(
            ILogger<TNC640DataService> logger,
            ITNC640CommunicationService communicationService,
            IOptions<TNC640Configuration> configuration)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
            _configuration = configuration?.Value ?? throw new ArgumentNullException(nameof(configuration));
            
            _latestData = new TNC640Data();
            _historicalData = new List<TNC640Data>();

            // 订阅通信服务事件
            _communicationService.DataUpdated += OnDataUpdated;
            _communicationService.ConnectionStatusChanged += OnConnectionStatusChanged;
            _communicationService.ErrorOccurred += OnErrorOccurred;
        }

        public TNC640Data GetLatestData()
        {
            lock (_dataLock)
            {
                return CloneData(_latestData);
            }
        }

        public async Task<TNC640Data> GetLatestDataAsync()
        {
            return await Task.FromResult(GetLatestData());
        }

        public async Task<IEnumerable<TNC640Data>> GetHistoricalDataAsync(DateTime startTime, DateTime endTime)
        {
            return await Task.Run(() =>
            {
                lock (_dataLock)
                {
                    return _historicalData
                        .Where(data => data.Timestamp >= startTime && data.Timestamp <= endTime)
                        .Select(CloneData)
                        .ToList();
                }
            });
        }

        public bool IsConnected()
        {
            return _communicationService.IsConnected;
        }

        public async Task StartAsync()
        {
            if (_isRunning)
            {
                _logger.LogWarning("数据服务已经在运行中");
                return;
            }

            _logger.LogInformation("启动TNC 640数据服务");

            try
            {
                // 连接到设备
                var connected = await _communicationService.ConnectAsync();
                if (!connected)
                {
                    throw new InvalidOperationException("无法连接到TNC 640设备");
                }

                // 开始数据采集
                await _communicationService.StartDataCollectionAsync();
                
                _isRunning = true;
                _logger.LogInformation("TNC 640数据服务启动成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动数据服务时发生错误");
                throw;
            }
        }

        public async Task StopAsync()
        {
            if (!_isRunning)
            {
                return;
            }

            _logger.LogInformation("停止TNC 640数据服务");

            try
            {
                // 停止数据采集
                await _communicationService.StopDataCollectionAsync();
                
                // 断开连接
                await _communicationService.DisconnectAsync();
                
                _isRunning = false;
                _logger.LogInformation("TNC 640数据服务已停止");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止数据服务时发生错误");
            }
        }

        private void OnDataUpdated(object? sender, TNC640Data data)
        {
            try
            {
                lock (_dataLock)
                {
                    // 更新最新数据
                    _latestData = CloneData(data);
                    
                    // 添加到历史数据（保留最近1000条记录）
                    _historicalData.Add(CloneData(data));
                    if (_historicalData.Count > 1000)
                    {
                        _historicalData.RemoveAt(0);
                    }
                }

                // 触发数据更新事件
                DataUpdated?.Invoke(this, CloneData(data));

                if (_configuration.Logging.EnableDataLog)
                {
                    _logger.LogDebug("数据已更新: 程序={ProgramName}, 状态={Status}, 时间={Timestamp}",
                        data.ProgramFileName, data.MachineStatus, data.Timestamp);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理数据更新时发生错误");
            }
        }

        private void OnConnectionStatusChanged(object? sender, bool isConnected)
        {
            _logger.LogInformation("连接状态变化: {Status}", isConnected ? "已连接" : "已断开");
            ConnectionStatusChanged?.Invoke(this, isConnected);
        }

        private void OnErrorOccurred(object? sender, string error)
        {
            _logger.LogError("通信错误: {Error}", error);
        }

        private TNC640Data CloneData(TNC640Data original)
        {
            return new TNC640Data
            {
                Timestamp = original.Timestamp,
                ProgramFileName = original.ProgramFileName,
                CurrentCycleTime = original.CurrentCycleTime,
                WorkpieceCount = original.WorkpieceCount,
                SpindleOverride = original.SpindleOverride,
                FeedOverride = original.FeedOverride,
                ActualSpindleSpeed = original.ActualSpindleSpeed,
                ActualFeedRate = original.ActualFeedRate,
                AlarmStatus = CloneAlarmStatus(original.AlarmStatus),
                MachineStatus = original.MachineStatus,
                IsDataValid = original.IsDataValid,
                IsConnected = original.IsConnected,
                LastError = original.LastError
            };
        }

        private AlarmStatus CloneAlarmStatus(AlarmStatus original)
        {
            return new AlarmStatus
            {
                HasAlarm = original.HasAlarm,
                AlarmCount = original.AlarmCount,
                Alarms = original.Alarms.Select(alarm => new AlarmInfo
                {
                    AlarmNumber = alarm.AlarmNumber,
                    AlarmType = alarm.AlarmType,
                    Description = alarm.Description,
                    OccurredTime = alarm.OccurredTime,
                    Severity = alarm.Severity
                }).ToList()
            };
        }

        public void Dispose()
        {
            StopAsync().Wait();
            
            // 取消订阅事件
            if (_communicationService != null)
            {
                _communicationService.DataUpdated -= OnDataUpdated;
                _communicationService.ConnectionStatusChanged -= OnConnectionStatusChanged;
                _communicationService.ErrorOccurred -= OnErrorOccurred;
            }
        }
    }
}
