# TNC 640数据采集器 - 快速启动指南

## 前提条件

1. 安装 .NET 8.0 SDK
2. 确保计算机与TNC 640设备在同一网络中

## 快速开始

### 1. 配置连接参数

编辑 `appsettings.json` 文件，修改TNC 640设备的IP地址：

```json
{
  "TNC640": {
    "IpAddress": "*************",  // 修改为您的TNC 640设备IP地址
    "Port": 19000,
    // ... 其他配置保持默认
  }
}
```

### 2. 运行测试脚本（推荐）

使用PowerShell运行测试脚本：

```powershell
cd TNC640DataCollector
.\testscript\RunTests.ps1
```

测试脚本提供以下选项：
- **选项1**: 启动TNC 640模拟器（用于测试，无需真实设备）
- **选项2**: 运行连接测试
- **选项3**: 启动数据采集器（连接真实设备）
- **选项4**: 查看配置文件
- **选项5**: 退出

### 3. 手动运行（可选）

#### 启动模拟器（测试用）
```bash
# 在一个终端中启动模拟器
dotnet run --project testscript/TNC640Simulator.cs
```

#### 运行数据采集器
```bash
# 在另一个终端中运行数据采集器
dotnet run
```

## 验证运行

### 成功连接的标志
- 控制台显示 "✓ 连接测试成功"
- 实时数据界面显示设备参数
- 连接状态显示为 "已连接"

### 常见问题

1. **连接失败**
   - 检查IP地址是否正确
   - 确认网络连通性（ping测试）
   - 验证TNC 640设备的LSV2服务是否启用

2. **数据显示异常**
   - 检查设备是否支持所请求的参数
   - 查看控制台错误信息
   - 调整数据采集间隔

## 数据输出示例

成功运行后，您将看到类似以下的实时数据：

```
=== TNC 640实时数据 ===
更新时间: 2024-09-26 09:30:15
连接状态: 已连接
数据有效: 是

--- 程序信息 ---
程序文件: TEST_PROGRAM.NC
循环时间: 00:02:05
工件计数: 42

--- 运行参数 ---
主轴倍率: 100.0%
进给倍率: 85.0%
主轴转速: 1500 RPM
进给速度: 350.0 mm/min

--- 设备状态 ---
运行状态: 运行中
报警状态: 正常
```

## 下一步

- 查看 `README.md` 了解详细功能说明
- 参考 `Examples/BasicUsageExample.cs` 学习如何在代码中使用
- 根据需要修改 `appsettings.json` 中的配置参数
- 集成到您的应用程序中

## 技术支持

如遇问题，请检查：
1. 控制台错误信息
2. 网络连接状态
3. TNC 640设备配置
4. 防火墙设置
