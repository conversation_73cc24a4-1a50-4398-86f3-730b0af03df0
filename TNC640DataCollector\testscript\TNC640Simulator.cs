using System.Net;
using System.Net.Sockets;
using System.Text;

namespace TNC640DataCollector.TestScript
{
    /// <summary>
    /// TNC 640模拟器，用于测试通信功能
    /// </summary>
    public class TNC640Simulator
    {
        private TcpListener? _tcpListener;
        private bool _isRunning;
        private readonly Random _random = new Random();
        private readonly Dictionary<string, string> _simulatedData;

        public TNC640Simulator()
        {
            _simulatedData = new Dictionary<string, string>
            {
                ["PGM\\ACT"] = "TEST_PROGRAM.NC",
                ["PGM\\CYCLETIME"] = "125.5",
                ["PGM\\PARTS"] = "42",
                ["CHANNEL\\SPINDLE\\OVERRIDE"] = "100.0",
                ["CHANNEL\\FEED\\OVERRIDE"] = "85.0",
                ["CHANNEL\\SPINDLE\\ACT"] = "1500.0",
                ["CHANNEL\\FEED\\ACT"] = "350.0",
                ["SYS\\ALARM\\COUNT"] = "0",
                ["SYS\\STATE"] = "RUNNING",
                ["SYS\\VERSION"] = "TNC640_SIM_V1.0"
            };
        }

        public async Task StartAsync(int port = 19000)
        {
            try
            {
                _tcpListener = new TcpListener(IPAddress.Any, port);
                _tcpListener.Start();
                _isRunning = true;

                Console.WriteLine($"TNC 640模拟器已启动，监听端口: {port}");
                Console.WriteLine("等待客户端连接...");

                while (_isRunning)
                {
                    var tcpClient = await _tcpListener.AcceptTcpClientAsync();
                    Console.WriteLine($"客户端已连接: {tcpClient.Client.RemoteEndPoint}");

                    // 在新线程中处理客户端
                    _ = Task.Run(() => HandleClientAsync(tcpClient));
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"模拟器启动失败: {ex.Message}");
            }
        }

        public void Stop()
        {
            _isRunning = false;
            _tcpListener?.Stop();
            Console.WriteLine("TNC 640模拟器已停止");
        }

        private async Task HandleClientAsync(TcpClient tcpClient)
        {
            try
            {
                using (tcpClient)
                using (var networkStream = tcpClient.GetStream())
                {
                    var buffer = new byte[1024];

                    while (tcpClient.Connected && _isRunning)
                    {
                        var bytesRead = await networkStream.ReadAsync(buffer, 0, buffer.Length);
                        if (bytesRead == 0) break;

                        var request = Encoding.ASCII.GetString(buffer, 0, bytesRead).Trim();
                        Console.WriteLine($"收到请求: {request}");

                        var response = ProcessRequest(request);
                        var responseBytes = Encoding.ASCII.GetBytes(response + "\r\n");

                        await networkStream.WriteAsync(responseBytes, 0, responseBytes.Length);
                        await networkStream.FlushAsync();

                        Console.WriteLine($"发送响应: {response}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理客户端时发生错误: {ex.Message}");
            }
        }

        private string ProcessRequest(string request)
        {
            try
            {
                // 解析LSV2命令格式: COMMAND PARAMETER
                var parts = request.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length < 2)
                {
                    return "ERROR INVALID_COMMAND";
                }

                var command = parts[0].ToUpper();
                var parameter = parts[1];

                switch (command)
                {
                    case "R": // 读取命令
                        return ProcessReadCommand(parameter);
                    
                    case "W": // 写入命令
                        return ProcessWriteCommand(parameter, parts.Length > 2 ? parts[2] : "");
                    
                    case "LOGIN":
                        return "OK LOGIN_SUCCESS";
                    
                    default:
                        return "ERROR UNKNOWN_COMMAND";
                }
            }
            catch (Exception ex)
            {
                return $"ERROR {ex.Message}";
            }
        }

        private string ProcessReadCommand(string parameter)
        {
            // 更新一些动态数据
            UpdateDynamicData();

            if (_simulatedData.TryGetValue(parameter, out var value))
            {
                return $"OK {value}";
            }

            // 处理报警查询
            if (parameter.StartsWith("SYS\\ALARM\\") && parameter != "SYS\\ALARM\\COUNT")
            {
                var alarmIndex = parameter.Substring("SYS\\ALARM\\".Length);
                if (int.TryParse(alarmIndex, out var index) && index == 0)
                {
                    // 模拟一个报警
                    return "OK 001|SYSTEM|Test alarm message|WARNING";
                }
                return "ERROR NO_ALARM";
            }

            return "ERROR PARAMETER_NOT_FOUND";
        }

        private string ProcessWriteCommand(string parameter, string value)
        {
            // 模拟写入操作
            if (_simulatedData.ContainsKey(parameter))
            {
                _simulatedData[parameter] = value;
                return "OK WRITE_SUCCESS";
            }

            return "ERROR PARAMETER_NOT_FOUND";
        }

        private void UpdateDynamicData()
        {
            // 模拟动态变化的数据
            var cycleTime = double.Parse(_simulatedData["PGM\\CYCLETIME"]) + 0.1;
            _simulatedData["PGM\\CYCLETIME"] = cycleTime.ToString("F1");

            // 随机变化主轴转速
            var spindleSpeed = 1500 + _random.Next(-50, 51);
            _simulatedData["CHANNEL\\SPINDLE\\ACT"] = spindleSpeed.ToString("F0");

            // 随机变化进给速度
            var feedRate = 350 + _random.Next(-20, 21);
            _simulatedData["CHANNEL\\FEED\\ACT"] = feedRate.ToString("F1");

            // 偶尔模拟报警
            if (_random.Next(0, 100) < 5) // 5%概率有报警
            {
                _simulatedData["SYS\\ALARM\\COUNT"] = "1";
            }
            else
            {
                _simulatedData["SYS\\ALARM\\COUNT"] = "0";
            }
        }

        public static async Task RunSimulatorAsync(string[] args)
        {
            var simulator = new TNC640Simulator();
            
            Console.WriteLine("=== TNC 640模拟器 ===");
            Console.WriteLine("按 Ctrl+C 停止模拟器");

            // 处理Ctrl+C
            Console.CancelKeyPress += (sender, e) =>
            {
                e.Cancel = true;
                simulator.Stop();
            };

            try
            {
                await simulator.StartAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"模拟器运行错误: {ex.Message}");
            }

            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
