using System.ComponentModel.DataAnnotations;

namespace TNC640DataCollector.Configuration
{
    /// <summary>
    /// TNC 640通信配置
    /// </summary>
    public class TNC640Configuration
    {
        /// <summary>
        /// 配置节名称
        /// </summary>
        public const string SectionName = "TNC640";

        /// <summary>
        /// TNC 640设备IP地址
        /// </summary>
        [Required]
        public string IpAddress { get; set; } = "*************";

        /// <summary>
        /// 通信端口号（LSV2协议默认端口：19000）
        /// </summary>
        [Range(1, 65535)]
        public int Port { get; set; } = 19000;

        /// <summary>
        /// 连接超时时间（毫秒）
        /// </summary>
        [Range(1000, 60000)]
        public int ConnectionTimeoutMs { get; set; } = 5000;

        /// <summary>
        /// 读取超时时间（毫秒）
        /// </summary>
        [Range(1000, 30000)]
        public int ReadTimeoutMs { get; set; } = 3000;

        /// <summary>
        /// 数据采集间隔（毫秒）
        /// </summary>
        [Range(100, 10000)]
        public int DataCollectionIntervalMs { get; set; } = 1000;

        /// <summary>
        /// 重连间隔（毫秒）
        /// </summary>
        [Range(1000, 60000)]
        public int ReconnectIntervalMs { get; set; } = 5000;

        /// <summary>
        /// 最大重连次数
        /// </summary>
        [Range(0, 100)]
        public int MaxReconnectAttempts { get; set; } = 10;

        /// <summary>
        /// 是否启用自动重连
        /// </summary>
        public bool EnableAutoReconnect { get; set; } = true;

        /// <summary>
        /// 是否启用数据验证
        /// </summary>
        public bool EnableDataValidation { get; set; } = true;

        /// <summary>
        /// 通信协议类型
        /// </summary>
        public CommunicationProtocol Protocol { get; set; } = CommunicationProtocol.LSV2;

        /// <summary>
        /// LSV2协议配置
        /// </summary>
        public LSV2Configuration LSV2 { get; set; } = new LSV2Configuration();

        /// <summary>
        /// 日志配置
        /// </summary>
        public LoggingConfiguration Logging { get; set; } = new LoggingConfiguration();
    }

    /// <summary>
    /// 通信协议类型枚举
    /// </summary>
    public enum CommunicationProtocol
    {
        /// <summary>
        /// LSV2协议（海德汉标准协议）
        /// </summary>
        LSV2,
        /// <summary>
        /// OPC UA协议
        /// </summary>
        OPCUA,
        /// <summary>
        /// Modbus TCP协议
        /// </summary>
        ModbusTCP
    }

    /// <summary>
    /// LSV2协议配置
    /// </summary>
    public class LSV2Configuration
    {
        /// <summary>
        /// 登录用户名
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 登录密码
        /// </summary>
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// 是否需要登录验证
        /// </summary>
        public bool RequireAuthentication { get; set; } = false;

        /// <summary>
        /// 命令超时时间（毫秒）
        /// </summary>
        [Range(100, 10000)]
        public int CommandTimeoutMs { get; set; } = 2000;

        /// <summary>
        /// 保持连接间隔（毫秒）
        /// </summary>
        [Range(5000, 60000)]
        public int KeepAliveIntervalMs { get; set; } = 30000;
    }

    /// <summary>
    /// 日志配置
    /// </summary>
    public class LoggingConfiguration
    {
        /// <summary>
        /// 是否启用通信日志
        /// </summary>
        public bool EnableCommunicationLog { get; set; } = false;

        /// <summary>
        /// 是否启用数据日志
        /// </summary>
        public bool EnableDataLog { get; set; } = true;

        /// <summary>
        /// 日志级别
        /// </summary>
        public string LogLevel { get; set; } = "Information";

        /// <summary>
        /// 日志文件路径
        /// </summary>
        public string LogFilePath { get; set; } = "logs/tnc640.log";
    }
}
