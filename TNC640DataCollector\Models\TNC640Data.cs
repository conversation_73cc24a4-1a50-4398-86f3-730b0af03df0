using System.ComponentModel.DataAnnotations;

namespace TNC640DataCollector.Models
{
    /// <summary>
    /// TNC 640数控系统实时数据模型
    /// </summary>
    public class TNC640Data
    {
        /// <summary>
        /// 数据采集时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 加工程序文件名（当前正在执行的NC程序名称）
        /// </summary>
        public string ProgramFileName { get; set; } = string.Empty;

        /// <summary>
        /// 当前加工循环时间（当前程序已运行的加工时长，单位：秒）
        /// </summary>
        public TimeSpan CurrentCycleTime { get; set; }

        /// <summary>
        /// 工件计数（已完成的工件数量）
        /// </summary>
        public int WorkpieceCount { get; set; }

        /// <summary>
        /// 主轴倍率（主轴转速倍率百分比，0-200%）
        /// </summary>
        [Range(0, 200)]
        public double SpindleOverride { get; set; }

        /// <summary>
        /// 切削倍率/进给倍率（进给速度倍率百分比，0-200%）
        /// </summary>
        [Range(0, 200)]
        public double FeedOverride { get; set; }

        /// <summary>
        /// 主轴实际速度（当前主轴转速RPM）
        /// </summary>
        public double ActualSpindleSpeed { get; set; }

        /// <summary>
        /// 切削实际速度（当前进给速度，单位：mm/min）
        /// </summary>
        public double ActualFeedRate { get; set; }

        /// <summary>
        /// 设备报警状态
        /// </summary>
        public AlarmStatus AlarmStatus { get; set; } = new AlarmStatus();

        /// <summary>
        /// 设备运行状态
        /// </summary>
        public MachineStatus MachineStatus { get; set; }

        /// <summary>
        /// 数据是否有效
        /// </summary>
        public bool IsDataValid { get; set; }

        /// <summary>
        /// 通信连接状态
        /// </summary>
        public bool IsConnected { get; set; }

        /// <summary>
        /// 最后一次通信错误信息
        /// </summary>
        public string? LastError { get; set; }
    }

    /// <summary>
    /// 设备报警状态
    /// </summary>
    public class AlarmStatus
    {
        /// <summary>
        /// 是否有报警
        /// </summary>
        public bool HasAlarm { get; set; }

        /// <summary>
        /// 报警数量
        /// </summary>
        public int AlarmCount { get; set; }

        /// <summary>
        /// 报警信息列表
        /// </summary>
        public List<AlarmInfo> Alarms { get; set; } = new List<AlarmInfo>();
    }

    /// <summary>
    /// 报警信息
    /// </summary>
    public class AlarmInfo
    {
        /// <summary>
        /// 报警编号
        /// </summary>
        public string AlarmNumber { get; set; } = string.Empty;

        /// <summary>
        /// 报警类型
        /// </summary>
        public AlarmType AlarmType { get; set; }

        /// <summary>
        /// 报警描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 报警发生时间
        /// </summary>
        public DateTime OccurredTime { get; set; }

        /// <summary>
        /// 报警严重程度
        /// </summary>
        public AlarmSeverity Severity { get; set; }
    }

    /// <summary>
    /// 报警类型枚举
    /// </summary>
    public enum AlarmType
    {
        /// <summary>
        /// 系统报警
        /// </summary>
        System,
        /// <summary>
        /// 程序报警
        /// </summary>
        Program,
        /// <summary>
        /// 操作报警
        /// </summary>
        Operation,
        /// <summary>
        /// 硬件报警
        /// </summary>
        Hardware,
        /// <summary>
        /// 通信报警
        /// </summary>
        Communication
    }

    /// <summary>
    /// 报警严重程度枚举
    /// </summary>
    public enum AlarmSeverity
    {
        /// <summary>
        /// 信息
        /// </summary>
        Info,
        /// <summary>
        /// 警告
        /// </summary>
        Warning,
        /// <summary>
        /// 错误
        /// </summary>
        Error,
        /// <summary>
        /// 严重错误
        /// </summary>
        Critical
    }

    /// <summary>
    /// 设备运行状态枚举
    /// </summary>
    public enum MachineStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        Unknown,
        /// <summary>
        /// 关机
        /// </summary>
        PowerOff,
        /// <summary>
        /// 开机
        /// </summary>
        PowerOn,
        /// <summary>
        /// 待机
        /// </summary>
        Standby,
        /// <summary>
        /// 运行中
        /// </summary>
        Running,
        /// <summary>
        /// 暂停
        /// </summary>
        Paused,
        /// <summary>
        /// 停止
        /// </summary>
        Stopped,
        /// <summary>
        /// 报警状态
        /// </summary>
        Alarm,
        /// <summary>
        /// 维护模式
        /// </summary>
        Maintenance
    }
}
