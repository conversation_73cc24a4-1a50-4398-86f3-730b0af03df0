using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TNC640DataCollector.Configuration;
using TNC640DataCollector.Interfaces;
using TNC640DataCollector.Services;

namespace TNC640DataCollector.TestScript
{
    /// <summary>
    /// TNC 640连接测试脚本
    /// </summary>
    public class TestTNC640Connection
    {
        public static async Task RunTestAsync(string[] args)
        {
            Console.WriteLine("=== TNC 640连接测试 ===");
            
            // 配置服务
            var services = new ServiceCollection();
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false)
                .Build();

            services.Configure<TNC640Configuration>(
                configuration.GetSection(TNC640Configuration.SectionName));
            
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Debug);
            });
            
            services.AddSingleton<ITNC640CommunicationService, LSV2CommunicationService>();

            var serviceProvider = services.BuildServiceProvider();
            var logger = serviceProvider.GetRequiredService<ILogger<TestTNC640Connection>>();
            var communicationService = serviceProvider.GetRequiredService<ITNC640CommunicationService>();

            try
            {
                // 测试连接
                Console.WriteLine("正在测试连接...");
                var connected = await communicationService.TestConnectionAsync();
                
                if (connected)
                {
                    Console.WriteLine("✓ 连接测试成功");
                    
                    // 测试数据获取
                    Console.WriteLine("正在获取实时数据...");
                    var data = await communicationService.GetRealTimeDataAsync();
                    
                    Console.WriteLine("\n=== 获取到的数据 ===");
                    Console.WriteLine($"时间戳: {data.Timestamp}");
                    Console.WriteLine($"程序文件: {data.ProgramFileName}");
                    Console.WriteLine($"循环时间: {data.CurrentCycleTime}");
                    Console.WriteLine($"工件计数: {data.WorkpieceCount}");
                    Console.WriteLine($"主轴倍率: {data.SpindleOverride}%");
                    Console.WriteLine($"进给倍率: {data.FeedOverride}%");
                    Console.WriteLine($"主轴转速: {data.ActualSpindleSpeed} RPM");
                    Console.WriteLine($"进给速度: {data.ActualFeedRate} mm/min");
                    Console.WriteLine($"设备状态: {data.MachineStatus}");
                    Console.WriteLine($"报警状态: {(data.AlarmStatus.HasAlarm ? "有报警" : "正常")}");
                    Console.WriteLine($"数据有效: {data.IsDataValid}");
                    Console.WriteLine($"连接状态: {data.IsConnected}");
                    
                    if (!string.IsNullOrEmpty(data.LastError))
                    {
                        Console.WriteLine($"最后错误: {data.LastError}");
                    }
                }
                else
                {
                    Console.WriteLine("✗ 连接测试失败");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "测试过程中发生错误");
                Console.WriteLine($"✗ 测试失败: {ex.Message}");
            }
            finally
            {
                communicationService?.Dispose();
                Console.WriteLine("\n按任意键退出...");
                Console.ReadKey();
            }
        }
    }
}
