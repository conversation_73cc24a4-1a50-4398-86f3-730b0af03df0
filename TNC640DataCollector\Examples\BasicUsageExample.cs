using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TNC640DataCollector.Configuration;
using TNC640DataCollector.Interfaces;
using TNC640DataCollector.Models;
using TNC640DataCollector.Services;

namespace TNC640DataCollector.Examples
{
    /// <summary>
    /// TNC 640数据采集器基本使用示例
    /// </summary>
    public class BasicUsageExample
    {
        /// <summary>
        /// 基本使用示例：连接设备并获取数据
        /// </summary>
        public static async Task BasicConnectionExample()
        {
            Console.WriteLine("=== TNC 640基本连接示例 ===");

            // 1. 配置服务
            var services = new ServiceCollection();
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false)
                .Build();

            services.Configure<TNC640Configuration>(
                configuration.GetSection(TNC640Configuration.SectionName));
            
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Information);
            });
            
            services.AddSingleton<ITNC640CommunicationService, LSV2CommunicationService>();
            services.AddSingleton<ITNC640DataService, TNC640DataService>();

            var serviceProvider = services.BuildServiceProvider();
            var dataService = serviceProvider.GetRequiredService<ITNC640DataService>();

            try
            {
                // 2. 启动数据服务
                Console.WriteLine("正在启动数据服务...");
                await dataService.StartAsync();
                Console.WriteLine("数据服务启动成功");

                // 3. 获取实时数据
                Console.WriteLine("\n正在获取实时数据...");
                var data = await dataService.GetLatestDataAsync();
                
                DisplayData(data);

                // 4. 等待一段时间以观察数据变化
                Console.WriteLine("\n监控数据变化（10秒）...");
                for (int i = 0; i < 10; i++)
                {
                    await Task.Delay(1000);
                    data = await dataService.GetLatestDataAsync();
                    Console.WriteLine($"[{i+1}/10] 程序: {data.ProgramFileName}, 状态: {data.MachineStatus}, 主轴: {data.ActualSpindleSpeed:F0} RPM");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: {ex.Message}");
            }
            finally
            {
                // 5. 停止数据服务
                await dataService.StopAsync();
                Console.WriteLine("\n数据服务已停止");
            }
        }

        /// <summary>
        /// 事件驱动示例：订阅数据更新事件
        /// </summary>
        public static async Task EventDrivenExample()
        {
            Console.WriteLine("=== TNC 640事件驱动示例 ===");

            var services = new ServiceCollection();
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false)
                .Build();

            services.Configure<TNC640Configuration>(
                configuration.GetSection(TNC640Configuration.SectionName));
            
            services.AddLogging(builder => builder.AddConsole());
            services.AddSingleton<ITNC640CommunicationService, LSV2CommunicationService>();
            services.AddSingleton<ITNC640DataService, TNC640DataService>();

            var serviceProvider = services.BuildServiceProvider();
            var dataService = serviceProvider.GetRequiredService<ITNC640DataService>();

            // 订阅事件
            dataService.DataUpdated += OnDataUpdated;
            dataService.ConnectionStatusChanged += OnConnectionStatusChanged;

            try
            {
                await dataService.StartAsync();
                Console.WriteLine("数据服务已启动，正在监听数据更新事件...");
                Console.WriteLine("按任意键停止监听");

                Console.ReadKey();
            }
            finally
            {
                // 取消订阅事件
                dataService.DataUpdated -= OnDataUpdated;
                dataService.ConnectionStatusChanged -= OnConnectionStatusChanged;
                
                await dataService.StopAsync();
                Console.WriteLine("\n数据服务已停止");
            }
        }

        /// <summary>
        /// 历史数据查询示例
        /// </summary>
        public static async Task HistoricalDataExample()
        {
            Console.WriteLine("=== TNC 640历史数据查询示例 ===");

            var services = new ServiceCollection();
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false)
                .Build();

            services.Configure<TNC640Configuration>(
                configuration.GetSection(TNC640Configuration.SectionName));
            
            services.AddLogging(builder => builder.AddConsole());
            services.AddSingleton<ITNC640CommunicationService, LSV2CommunicationService>();
            services.AddSingleton<ITNC640DataService, TNC640DataService>();

            var serviceProvider = services.BuildServiceProvider();
            var dataService = serviceProvider.GetRequiredService<ITNC640DataService>();

            try
            {
                await dataService.StartAsync();
                Console.WriteLine("数据服务已启动，正在收集数据...");

                // 等待收集一些数据
                await Task.Delay(10000);

                // 查询最近5分钟的历史数据
                var endTime = DateTime.Now;
                var startTime = endTime.AddMinutes(-5);
                
                Console.WriteLine($"\n查询历史数据: {startTime:HH:mm:ss} - {endTime:HH:mm:ss}");
                var historicalData = await dataService.GetHistoricalDataAsync(startTime, endTime);
                
                Console.WriteLine($"找到 {historicalData.Count()} 条历史记录:");
                foreach (var data in historicalData.Take(5)) // 显示前5条
                {
                    Console.WriteLine($"[{data.Timestamp:HH:mm:ss}] 程序: {data.ProgramFileName}, 主轴: {data.ActualSpindleSpeed:F0} RPM");
                }
            }
            finally
            {
                await dataService.StopAsync();
            }
        }

        private static void OnDataUpdated(object? sender, TNC640Data data)
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 数据更新 - 程序: {data.ProgramFileName}, 状态: {data.MachineStatus}");
            
            if (data.AlarmStatus.HasAlarm)
            {
                Console.WriteLine($"  ⚠️ 报警: {data.AlarmStatus.AlarmCount} 个报警");
            }
        }

        private static void OnConnectionStatusChanged(object? sender, bool isConnected)
        {
            var status = isConnected ? "已连接" : "已断开";
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 连接状态变化: {status}");
        }

        private static void DisplayData(TNC640Data data)
        {
            Console.WriteLine("\n=== 当前数据 ===");
            Console.WriteLine($"时间戳: {data.Timestamp:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"连接状态: {(data.IsConnected ? "已连接" : "已断开")}");
            Console.WriteLine($"数据有效: {(data.IsDataValid ? "是" : "否")}");
            Console.WriteLine($"程序文件: {data.ProgramFileName}");
            Console.WriteLine($"循环时间: {data.CurrentCycleTime:hh\\:mm\\:ss}");
            Console.WriteLine($"工件计数: {data.WorkpieceCount}");
            Console.WriteLine($"主轴倍率: {data.SpindleOverride:F1}%");
            Console.WriteLine($"进给倍率: {data.FeedOverride:F1}%");
            Console.WriteLine($"主轴转速: {data.ActualSpindleSpeed:F0} RPM");
            Console.WriteLine($"进给速度: {data.ActualFeedRate:F1} mm/min");
            Console.WriteLine($"设备状态: {data.MachineStatus}");
            Console.WriteLine($"报警状态: {(data.AlarmStatus.HasAlarm ? $"有报警 ({data.AlarmStatus.AlarmCount}个)" : "正常")}");
            
            if (!string.IsNullOrEmpty(data.LastError))
            {
                Console.WriteLine($"最后错误: {data.LastError}");
            }
        }
    }
}
