using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using TNC640DataCollector.Configuration;
using TNC640DataCollector.Interfaces;
using TNC640DataCollector.Models;

namespace TNC640DataCollector.Services
{
    /// <summary>
    /// TNC 640托管服务，负责管理数据采集的生命周期
    /// </summary>
    public class TNC640HostedService : BackgroundService
    {
        private readonly ILogger<TNC640HostedService> _logger;
        private readonly ITNC640DataService _dataService;
        private readonly TNC640Configuration _configuration;
        private readonly IHostApplicationLifetime _applicationLifetime;

        public TNC640HostedService(
            ILogger<TNC640HostedService> logger,
            ITNC640DataService dataService,
            IOptions<TNC640Configuration> configuration,
            IHostApplicationLifetime applicationLifetime)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            _configuration = configuration?.Value ?? throw new ArgumentNullException(nameof(configuration));
            _applicationLifetime = applicationLifetime ?? throw new ArgumentNullException(nameof(applicationLifetime));

            // 订阅数据更新事件
            _dataService.DataUpdated += OnDataUpdated;
            _dataService.ConnectionStatusChanged += OnConnectionStatusChanged;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("TNC 640托管服务开始执行");

            try
            {
                // 启动数据服务
                await _dataService.StartAsync();

                // 显示初始状态
                DisplayStatus();

                // 等待取消信号
                while (!stoppingToken.IsCancellationRequested)
                {
                    await Task.Delay(5000, stoppingToken); // 每5秒检查一次状态
                    
                    if (_dataService.IsConnected())
                    {
                        var latestData = await _dataService.GetLatestDataAsync();
                        DisplayRealTimeData(latestData);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("TNC 640托管服务被取消");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "TNC 640托管服务执行过程中发生错误");
                _applicationLifetime.StopApplication();
            }
            finally
            {
                await _dataService.StopAsync();
                _logger.LogInformation("TNC 640托管服务已停止");
            }
        }

        private void OnDataUpdated(object? sender, TNC640Data data)
        {
            // 这里可以添加数据处理逻辑，比如保存到数据库、发送到其他系统等
            if (_configuration.Logging.EnableDataLog)
            {
                _logger.LogDebug("收到数据更新: {ProgramName} - {Status}", 
                    data.ProgramFileName, data.MachineStatus);
            }
        }

        private void OnConnectionStatusChanged(object? sender, bool isConnected)
        {
            var status = isConnected ? "已连接" : "已断开";
            _logger.LogInformation("TNC 640连接状态: {Status}", status);
            
            Console.WriteLine($"\n[{DateTime.Now:HH:mm:ss}] 连接状态: {status}");
            
            if (!isConnected && _configuration.EnableAutoReconnect)
            {
                _logger.LogInformation("将在 {Interval}ms 后尝试重连", _configuration.ReconnectIntervalMs);
            }
        }

        private void DisplayStatus()
        {
            Console.WriteLine("\n=== TNC 640数据采集器状态 ===");
            Console.WriteLine($"设备地址: {_configuration.IpAddress}:{_configuration.Port}");
            Console.WriteLine($"采集间隔: {_configuration.DataCollectionIntervalMs}ms");
            Console.WriteLine($"协议类型: {_configuration.Protocol}");
            Console.WriteLine($"自动重连: {(_configuration.EnableAutoReconnect ? "启用" : "禁用")}");
            Console.WriteLine("================================\n");
        }

        private void DisplayRealTimeData(TNC640Data data)
        {
            if (!data.IsDataValid)
            {
                return;
            }

            Console.Clear();
            Console.WriteLine("=== TNC 640实时数据 ===");
            Console.WriteLine($"更新时间: {data.Timestamp:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"连接状态: {(data.IsConnected ? "已连接" : "已断开")}");
            Console.WriteLine($"数据有效: {(data.IsDataValid ? "是" : "否")}");
            Console.WriteLine();
            
            Console.WriteLine("--- 程序信息 ---");
            Console.WriteLine($"程序文件: {data.ProgramFileName}");
            Console.WriteLine($"循环时间: {data.CurrentCycleTime:hh\\:mm\\:ss}");
            Console.WriteLine($"工件计数: {data.WorkpieceCount}");
            Console.WriteLine();
            
            Console.WriteLine("--- 运行参数 ---");
            Console.WriteLine($"主轴倍率: {data.SpindleOverride:F1}%");
            Console.WriteLine($"进给倍率: {data.FeedOverride:F1}%");
            Console.WriteLine($"主轴转速: {data.ActualSpindleSpeed:F0} RPM");
            Console.WriteLine($"进给速度: {data.ActualFeedRate:F1} mm/min");
            Console.WriteLine();
            
            Console.WriteLine("--- 设备状态 ---");
            Console.WriteLine($"运行状态: {GetMachineStatusText(data.MachineStatus)}");
            Console.WriteLine($"报警状态: {(data.AlarmStatus.HasAlarm ? $"有报警 ({data.AlarmStatus.AlarmCount}个)" : "正常")}");
            
            if (data.AlarmStatus.HasAlarm && data.AlarmStatus.Alarms.Any())
            {
                Console.WriteLine("\n--- 报警信息 ---");
                foreach (var alarm in data.AlarmStatus.Alarms.Take(3)) // 显示前3个报警
                {
                    Console.WriteLine($"[{alarm.AlarmNumber}] {alarm.Description} ({alarm.Severity})");
                }
            }
            
            if (!string.IsNullOrEmpty(data.LastError))
            {
                Console.WriteLine($"\n最后错误: {data.LastError}");
            }
            
            Console.WriteLine("\n按 Ctrl+C 退出");
        }

        private string GetMachineStatusText(MachineStatus status)
        {
            return status switch
            {
                MachineStatus.PowerOff => "关机",
                MachineStatus.PowerOn => "开机",
                MachineStatus.Standby => "待机",
                MachineStatus.Running => "运行中",
                MachineStatus.Paused => "暂停",
                MachineStatus.Stopped => "停止",
                MachineStatus.Alarm => "报警",
                MachineStatus.Maintenance => "维护",
                _ => "未知"
            };
        }

        public override void Dispose()
        {
            // 取消订阅事件
            if (_dataService != null)
            {
                _dataService.DataUpdated -= OnDataUpdated;
                _dataService.ConnectionStatusChanged -= OnConnectionStatusChanged;
            }
            
            base.Dispose();
        }
    }
}
