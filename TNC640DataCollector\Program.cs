﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using TNC640DataCollector.Configuration;
using TNC640DataCollector.Interfaces;
using TNC640DataCollector.Services;

namespace TNC640DataCollector
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== TNC 640数据采集器 ===");
            Console.WriteLine("正在启动...");

            try
            {
                // 创建主机构建器
                var hostBuilder = Host.CreateDefaultBuilder(args)
                    .ConfigureAppConfiguration((context, config) =>
                    {
                        config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                    })
                    .ConfigureServices((context, services) =>
                    {
                        // 注册配置
                        services.Configure<TNC640Configuration>(
                            context.Configuration.GetSection(TNC640Configuration.SectionName));

                        // 注册服务
                        services.AddSingleton<ITNC640CommunicationService, LSV2CommunicationService>();
                        services.AddSingleton<ITNC640DataService, TNC640DataService>();
                        services.AddHostedService<TNC640HostedService>();
                    })
                    .ConfigureLogging((context, logging) =>
                    {
                        logging.ClearProviders();
                        logging.AddConsole();
                        logging.SetMinimumLevel(LogLevel.Information);
                    });

                // 构建并运行主机
                var host = hostBuilder.Build();

                Console.WriteLine("服务已启动，按 Ctrl+C 退出");
                await host.RunAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"启动失败: {ex.Message}");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
        }
    }
}
