using TNC640DataCollector.Models;

namespace TNC640DataCollector.Interfaces
{
    /// <summary>
    /// TNC 640数据服务接口
    /// </summary>
    public interface ITNC640DataService
    {
        /// <summary>
        /// 获取最新的TNC 640数据
        /// </summary>
        /// <returns>最新的TNC 640数据</returns>
        TNC640Data GetLatestData();

        /// <summary>
        /// 获取最新的TNC 640数据（异步）
        /// </summary>
        /// <returns>最新的TNC 640数据</returns>
        Task<TNC640Data> GetLatestDataAsync();

        /// <summary>
        /// 获取历史数据
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>历史数据列表</returns>
        Task<IEnumerable<TNC640Data>> GetHistoricalDataAsync(DateTime startTime, DateTime endTime);

        /// <summary>
        /// 获取连接状态
        /// </summary>
        /// <returns>连接状态</returns>
        bool IsConnected();

        /// <summary>
        /// 数据更新事件
        /// </summary>
        event EventHandler<TNC640Data> DataUpdated;

        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        event EventHandler<bool> ConnectionStatusChanged;

        /// <summary>
        /// 启动数据服务
        /// </summary>
        /// <returns>启动任务</returns>
        Task StartAsync();

        /// <summary>
        /// 停止数据服务
        /// </summary>
        /// <returns>停止任务</returns>
        Task StopAsync();
    }
}
