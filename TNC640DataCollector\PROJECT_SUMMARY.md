# TNC 640数据采集器项目总结

## 项目概述

本项目是一个基于.NET 8.0的海德汉(HEIDENHAIN) TNC 640数控系统实时数据采集解决方案。项目实现了与TNC 640设备的LSV2协议通信，能够实时获取设备的各项运行参数。

## 已实现的核心功能

### 1. 数据采集功能
- ✅ 加工程序文件名获取
- ✅ 当前加工循环时间监控
- ✅ 工件计数统计
- ✅ 主轴倍率实时监控
- ✅ 进给倍率实时监控
- ✅ 主轴实际转速获取
- ✅ 进给实际速度获取
- ✅ 设备报警状态检测
- ✅ 设备运行状态监控

### 2. 通信功能
- ✅ LSV2协议通信实现
- ✅ TCP/IP网络连接管理
- ✅ 自动重连机制
- ✅ 连接状态监控
- ✅ 通信错误处理

### 3. 数据管理功能
- ✅ 实时数据缓存
- ✅ 历史数据存储（内存）
- ✅ 数据验证机制
- ✅ 事件驱动的数据更新通知

### 4. 配置管理
- ✅ JSON配置文件支持
- ✅ 灵活的连接参数配置
- ✅ 采集间隔可配置
- ✅ 日志级别可配置

## 项目架构

### 核心组件

1. **数据模型层 (Models)**
   - `TNC640Data.cs`: 主要数据模型
   - `AlarmStatus.cs`: 报警状态模型
   - 枚举类型定义

2. **配置层 (Configuration)**
   - `TNC640Configuration.cs`: 通信配置类
   - `LSV2Configuration.cs`: LSV2协议配置
   - `LoggingConfiguration.cs`: 日志配置

3. **接口层 (Interfaces)**
   - `ITNC640CommunicationService.cs`: 通信服务接口
   - `ITNC640DataService.cs`: 数据服务接口

4. **服务层 (Services)**
   - `LSV2CommunicationService.cs`: LSV2协议通信实现
   - `TNC640DataService.cs`: 数据服务实现
   - `TNC640HostedService.cs`: 托管服务实现

5. **测试工具 (TestScript)**
   - `TNC640Simulator.cs`: 设备模拟器
   - `TestTNC640Connection.cs`: 连接测试工具
   - `RunTests.ps1`: 自动化测试脚本

6. **使用示例 (Examples)**
   - `BasicUsageExample.cs`: 基本使用示例

## 技术特性

### 设计模式
- **依赖注入**: 使用Microsoft.Extensions.DependencyInjection
- **事件驱动**: 基于事件的数据更新通知
- **异步编程**: 全面使用async/await模式
- **接口隔离**: 清晰的接口定义和实现分离

### 错误处理
- **异常捕获**: 完善的异常处理机制
- **自动重连**: 网络断线自动重连
- **错误日志**: 详细的错误信息记录
- **状态监控**: 实时连接状态监控

### 性能优化
- **异步操作**: 非阻塞的数据采集
- **内存管理**: 合理的数据缓存策略
- **资源释放**: 正确的资源清理机制

## 配置说明

### 关键配置参数
```json
{
  "TNC640": {
    "IpAddress": "*************",        // 设备IP地址
    "Port": 19000,                       // 通信端口
    "DataCollectionIntervalMs": 1000,    // 采集间隔(毫秒)
    "EnableAutoReconnect": true,         // 自动重连
    "Protocol": "LSV2"                   // 通信协议
  }
}
```

## 使用方式

### 1. 直接运行
```bash
dotnet run
```

### 2. 使用测试脚本
```powershell
.\testscript\RunTests.ps1
```

### 3. 编程集成
```csharp
var dataService = serviceProvider.GetRequiredService<ITNC640DataService>();
await dataService.StartAsync();
var data = await dataService.GetLatestDataAsync();
```

## 测试支持

### 模拟器功能
- 完整的TNC 640设备模拟
- LSV2协议响应模拟
- 动态数据变化模拟
- 报警状态模拟

### 测试工具
- 连接测试脚本
- 自动化测试流程
- 配置验证工具

## 扩展性

### 协议扩展
- 接口化设计支持多种协议
- 可轻松添加OPC UA、Modbus等协议
- 插件式架构设计

### 数据扩展
- 模型化的数据结构
- 易于添加新的数据参数
- 灵活的数据验证机制

### 功能扩展
- 事件驱动架构支持自定义处理
- 可集成数据库存储
- 支持Web API接口

## 部署要求

### 系统要求
- .NET 8.0 Runtime
- Windows/Linux/macOS支持
- 网络连接到TNC 640设备

### 网络要求
- TCP/IP连接
- 默认端口19000
- 防火墙配置

## 维护指南

### 日志管理
- 结构化日志输出
- 可配置的日志级别
- 文件和控制台日志支持

### 监控建议
- 连接状态监控
- 数据采集频率监控
- 错误率统计

### 故障排除
- 详细的错误信息
- 网络连接诊断
- 配置验证工具

## 未来改进方向

### 功能增强
- [ ] 数据库持久化存储
- [ ] Web管理界面
- [ ] RESTful API接口
- [ ] 多设备并发支持

### 性能优化
- [ ] 数据压缩传输
- [ ] 缓存策略优化
- [ ] 内存使用优化

### 协议支持
- [ ] OPC UA协议支持
- [ ] Modbus TCP协议支持
- [ ] 自定义协议扩展

## 总结

本项目成功实现了与TNC 640数控系统的实时数据通信，提供了完整的数据采集解决方案。项目具有良好的架构设计、完善的错误处理机制和丰富的测试工具，可以直接用于生产环境或作为二次开发的基础平台。
