# TNC 640数据采集器

这是一个基于.NET 8.0的海德汉(HEIDENHAIN) TNC 640数控系统实时数据采集项目。

## 功能特性

### 核心功能
- 与TNC 640数控系统建立通信连接（支持LSV2协议）
- 实时获取以下参数数据：
  - 加工程序文件名（当前正在执行的NC程序名称）
  - 当前加工循环时间（当前程序已运行的加工时长）
  - 工件计数（已完成的工件数量）
  - 主轴倍率（主轴转速倍率百分比）
  - 切削倍率/进给倍率（进给速度倍率百分比）
  - 主轴实际速度（当前主轴转速RPM）
  - 切削实际速度（当前进给速度）
  - 设备报警状态（是否有报警及报警信息）
  - 设备运行状态（开机/运行/待机/停止等状态）

### 技术特性
- 基于.NET 8.0框架
- 支持LSV2协议通信
- 实时数据采集和更新
- 完善的错误处理和异常管理
- 自动重连机制
- 可配置的采集间隔
- 事件驱动的数据更新通知
- 结构化的日志记录

## 项目结构

```
TNC640DataCollector/
├── Models/                     # 数据模型
│   └── TNC640Data.cs          # TNC 640数据模型
├── Configuration/              # 配置类
│   └── TNC640Configuration.cs # 通信配置
├── Interfaces/                 # 接口定义
│   ├── ITNC640CommunicationService.cs # 通信服务接口
│   └── ITNC640DataService.cs  # 数据服务接口
├── Services/                   # 服务实现
│   ├── LSV2CommunicationService.cs # LSV2协议通信服务
│   ├── TNC640DataService.cs   # 数据服务实现
│   └── TNC640HostedService.cs # 托管服务
├── testscript/                 # 测试脚本
│   ├── TestTNC640Connection.cs # 连接测试脚本
│   └── TNC640Simulator.cs     # TNC 640模拟器
├── appsettings.json           # 配置文件
├── Program.cs                 # 主程序入口
└── README.md                  # 项目说明
```

## 配置说明

### appsettings.json配置项

```json
{
  "TNC640": {
    "IpAddress": "*************",        // TNC 640设备IP地址
    "Port": 19000,                       // 通信端口（LSV2默认19000）
    "ConnectionTimeoutMs": 5000,         // 连接超时时间（毫秒）
    "ReadTimeoutMs": 3000,              // 读取超时时间（毫秒）
    "DataCollectionIntervalMs": 1000,    // 数据采集间隔（毫秒）
    "ReconnectIntervalMs": 5000,        // 重连间隔（毫秒）
    "MaxReconnectAttempts": 10,         // 最大重连次数
    "EnableAutoReconnect": true,        // 是否启用自动重连
    "EnableDataValidation": true,       // 是否启用数据验证
    "Protocol": "LSV2",                 // 通信协议类型
    "LSV2": {
      "Username": "",                   // 登录用户名（如需要）
      "Password": "",                   // 登录密码（如需要）
      "RequireAuthentication": false,   // 是否需要登录验证
      "CommandTimeoutMs": 2000,        // 命令超时时间（毫秒）
      "KeepAliveIntervalMs": 30000     // 保持连接间隔（毫秒）
    },
    "Logging": {
      "EnableCommunicationLog": false,  // 是否启用通信日志
      "EnableDataLog": true,           // 是否启用数据日志
      "LogLevel": "Information",       // 日志级别
      "LogFilePath": "logs/tnc640.log" // 日志文件路径
    }
  }
}
```

## 使用方法

### 1. 编译项目

```bash
dotnet build
```

### 2. 运行主程序

```bash
dotnet run
```

### 3. 运行测试

#### 启动TNC 640模拟器（用于测试）
```bash
dotnet run --project testscript/TNC640Simulator.cs
```

#### 运行连接测试
```bash
dotnet run --project testscript/TestTNC640Connection.cs
```

## 开发指南

### 添加新的数据参数

1. 在`Models/TNC640Data.cs`中添加新的属性
2. 在`LSV2CommunicationService.cs`中添加对应的获取方法
3. 在`GetRealTimeDataAsync`方法中调用新的获取方法

### 扩展通信协议

1. 实现`ITNC640CommunicationService`接口
2. 在`Program.cs`中注册新的通信服务
3. 更新配置文件以支持新协议

### 自定义数据处理

1. 订阅`ITNC640DataService.DataUpdated`事件
2. 在事件处理程序中实现自定义逻辑（如数据库保存、API调用等）

## 注意事项

1. **网络配置**：确保运行程序的计算机与TNC 640设备在同一网络中，且网络连通正常
2. **防火墙设置**：确保防火墙允许程序访问指定端口（默认19000）
3. **设备设置**：确保TNC 640设备已启用LSV2协议并配置正确的网络参数
4. **权限要求**：某些操作可能需要在TNC 640设备上配置相应的用户权限

## 故障排除

### 常见问题

1. **连接失败**
   - 检查IP地址和端口配置是否正确
   - 确认网络连通性（可使用ping命令测试）
   - 检查TNC 640设备的LSV2服务是否启用

2. **数据获取失败**
   - 检查LSV2命令格式是否正确
   - 确认设备支持所请求的参数
   - 查看日志文件获取详细错误信息

3. **频繁断线重连**
   - 调整连接超时和读取超时参数
   - 检查网络稳定性
   - 适当增加数据采集间隔

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者
