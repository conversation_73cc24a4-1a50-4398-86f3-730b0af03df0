# TNC 640数据采集器测试脚本

Write-Host "=== TNC 640数据采集器测试脚本 ===" -ForegroundColor Green
Write-Host ""

# 检查.NET 8.0是否安装
Write-Host "检查.NET 8.0环境..." -ForegroundColor Yellow
try {
    $dotnetVersion = dotnet --version
    Write-Host "✓ .NET版本: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ 未找到.NET SDK，请先安装.NET 8.0 SDK" -ForegroundColor Red
    exit 1
}

# 构建项目
Write-Host ""
Write-Host "构建项目..." -ForegroundColor Yellow
$buildResult = dotnet build --configuration Release
if ($LASTEXITCODE -ne 0) {
    Write-Host "✗ 项目构建失败" -ForegroundColor Red
    exit 1
}
Write-Host "✓ 项目构建成功" -ForegroundColor Green

# 显示菜单
Write-Host ""
Write-Host "请选择要执行的操作:" -ForegroundColor Cyan
Write-Host "1. 启动TNC 640模拟器（用于测试）"
Write-Host "2. 运行连接测试"
Write-Host "3. 启动数据采集器（连接真实设备）"
Write-Host "4. 查看配置文件"
Write-Host "5. 退出"
Write-Host ""

do {
    $choice = Read-Host "请输入选择 (1-5)"
    
    switch ($choice) {
        "1" {
            Write-Host ""
            Write-Host "启动TNC 640模拟器..." -ForegroundColor Yellow
            Write-Host "模拟器将在端口19000上监听连接"
            Write-Host "按Ctrl+C停止模拟器"
            Write-Host ""
            
            # 创建临时的模拟器启动程序
            $simulatorCode = @"
using TNC640DataCollector.TestScript;

namespace TNC640DataCollector.TestRunner
{
    class SimulatorRunner
    {
        static async Task Main(string[] args)
        {
            await TNC640Simulator.RunSimulatorAsync(args);
        }
    }
}
"@
            $simulatorCode | Out-File -FilePath "SimulatorRunner.cs" -Encoding UTF8
            
            try {
                dotnet run SimulatorRunner.cs
            } finally {
                Remove-Item "SimulatorRunner.cs" -ErrorAction SilentlyContinue
            }
            break
        }
        
        "2" {
            Write-Host ""
            Write-Host "运行连接测试..." -ForegroundColor Yellow
            Write-Host "请确保TNC 640设备或模拟器正在运行"
            Write-Host ""
            
            # 创建临时的测试启动程序
            $testCode = @"
using TNC640DataCollector.TestScript;

namespace TNC640DataCollector.TestRunner
{
    class TestRunner
    {
        static async Task Main(string[] args)
        {
            await TestTNC640Connection.RunTestAsync(args);
        }
    }
}
"@
            $testCode | Out-File -FilePath "TestRunner.cs" -Encoding UTF8
            
            try {
                dotnet run TestRunner.cs
            } finally {
                Remove-Item "TestRunner.cs" -ErrorAction SilentlyContinue
            }
            break
        }
        
        "3" {
            Write-Host ""
            Write-Host "启动数据采集器..." -ForegroundColor Yellow
            Write-Host "请确保已正确配置appsettings.json中的设备连接参数"
            Write-Host "按Ctrl+C停止数据采集"
            Write-Host ""
            
            dotnet run --configuration Release
            break
        }
        
        "4" {
            Write-Host ""
            Write-Host "当前配置文件内容:" -ForegroundColor Yellow
            Write-Host "========================" -ForegroundColor Gray
            Get-Content "appsettings.json" | Write-Host
            Write-Host "========================" -ForegroundColor Gray
            Write-Host ""
            Write-Host "如需修改配置，请编辑 appsettings.json 文件" -ForegroundColor Cyan
            break
        }
        
        "5" {
            Write-Host ""
            Write-Host "退出测试脚本" -ForegroundColor Yellow
            exit 0
        }
        
        default {
            Write-Host "无效选择，请输入1-5之间的数字" -ForegroundColor Red
        }
    }
    
    if ($choice -ne "5") {
        Write-Host ""
        Write-Host "按任意键返回主菜单..." -ForegroundColor Gray
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        Write-Host ""
        Write-Host "请选择要执行的操作:" -ForegroundColor Cyan
        Write-Host "1. 启动TNC 640模拟器（用于测试）"
        Write-Host "2. 运行连接测试"
        Write-Host "3. 启动数据采集器（连接真实设备）"
        Write-Host "4. 查看配置文件"
        Write-Host "5. 退出"
        Write-Host ""
    }
    
} while ($choice -ne "5")
