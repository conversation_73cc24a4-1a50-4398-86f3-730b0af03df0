using TNC640DataCollector.Models;

namespace TNC640DataCollector.Interfaces
{
    /// <summary>
    /// TNC 640通信服务接口
    /// </summary>
    public interface ITNC640CommunicationService : IDisposable
    {
        /// <summary>
        /// 连接状态
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        event EventHandler<bool> ConnectionStatusChanged;

        /// <summary>
        /// 数据更新事件
        /// </summary>
        event EventHandler<TNC640Data> DataUpdated;

        /// <summary>
        /// 错误发生事件
        /// </summary>
        event EventHandler<string> ErrorOccurred;

        /// <summary>
        /// 连接到TNC 640设备
        /// </summary>
        /// <returns>连接是否成功</returns>
        Task<bool> ConnectAsync();

        /// <summary>
        /// 断开与TNC 640设备的连接
        /// </summary>
        /// <returns>断开连接任务</returns>
        Task DisconnectAsync();

        /// <summary>
        /// 获取实时数据
        /// </summary>
        /// <returns>TNC 640实时数据</returns>
        Task<TNC640Data> GetRealTimeDataAsync();

        /// <summary>
        /// 开始数据采集
        /// </summary>
        /// <returns>开始采集任务</returns>
        Task StartDataCollectionAsync();

        /// <summary>
        /// 停止数据采集
        /// </summary>
        /// <returns>停止采集任务</returns>
        Task StopDataCollectionAsync();

        /// <summary>
        /// 测试连接
        /// </summary>
        /// <returns>连接测试结果</returns>
        Task<bool> TestConnectionAsync();
    }
}
