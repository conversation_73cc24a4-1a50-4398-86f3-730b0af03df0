using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net.Sockets;
using System.Text;
using TNC640DataCollector.Configuration;
using TNC640DataCollector.Interfaces;
using TNC640DataCollector.Models;

namespace TNC640DataCollector.Services
{
    /// <summary>
    /// LSV2协议通信服务实现
    /// </summary>
    public class LSV2CommunicationService : ITNC640CommunicationService
    {
        private readonly ILogger<LSV2CommunicationService> _logger;
        private readonly TNC640Configuration _configuration;
        private TcpClient? _tcpClient;
        private NetworkStream? _networkStream;
        private bool _isConnected;
        private bool _isDataCollectionRunning;
        private CancellationTokenSource? _cancellationTokenSource;
        private Timer? _dataCollectionTimer;
        private readonly object _lockObject = new object();

        public bool IsConnected => _isConnected;

        public event EventHandler<bool>? ConnectionStatusChanged;
        public event EventHandler<TNC640Data>? DataUpdated;
        public event EventHandler<string>? ErrorOccurred;

        public LSV2CommunicationService(
            ILogger<LSV2CommunicationService> logger,
            IOptions<TNC640Configuration> configuration)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration?.Value ?? throw new ArgumentNullException(nameof(configuration));
        }

        public async Task<bool> ConnectAsync()
        {
            try
            {
                lock (_lockObject)
                {
                    if (_isConnected)
                    {
                        _logger.LogInformation("已经连接到TNC 640设备");
                        return true;
                    }
                }

                _logger.LogInformation("正在连接到TNC 640设备 {IpAddress}:{Port}", 
                    _configuration.IpAddress, _configuration.Port);

                _tcpClient = new TcpClient();
                _tcpClient.ReceiveTimeout = _configuration.ReadTimeoutMs;
                _tcpClient.SendTimeout = _configuration.ReadTimeoutMs;

                await _tcpClient.ConnectAsync(_configuration.IpAddress, _configuration.Port);
                _networkStream = _tcpClient.GetStream();

                // 执行LSV2协议握手
                if (await PerformLSV2HandshakeAsync())
                {
                    lock (_lockObject)
                    {
                        _isConnected = true;
                    }

                    _logger.LogInformation("成功连接到TNC 640设备");
                    ConnectionStatusChanged?.Invoke(this, true);
                    return true;
                }
                else
                {
                    await DisconnectAsync();
                    _logger.LogError("LSV2协议握手失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "连接TNC 640设备时发生错误");
                ErrorOccurred?.Invoke(this, $"连接错误: {ex.Message}");
                await DisconnectAsync();
                return false;
            }
        }

        public async Task DisconnectAsync()
        {
            try
            {
                lock (_lockObject)
                {
                    _isConnected = false;
                }

                if (_networkStream != null)
                {
                    await _networkStream.DisposeAsync();
                    _networkStream = null;
                }

                if (_tcpClient != null)
                {
                    _tcpClient.Close();
                    _tcpClient.Dispose();
                    _tcpClient = null;
                }

                _logger.LogInformation("已断开与TNC 640设备的连接");
                ConnectionStatusChanged?.Invoke(this, false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "断开连接时发生错误");
            }
        }

        public async Task<TNC640Data> GetRealTimeDataAsync()
        {
            if (!_isConnected || _networkStream == null)
            {
                throw new InvalidOperationException("未连接到TNC 640设备");
            }

            try
            {
                var data = new TNC640Data
                {
                    Timestamp = DateTime.Now,
                    IsConnected = true,
                    IsDataValid = true
                };

                // 获取各项数据
                data.ProgramFileName = await GetProgramFileNameAsync();
                data.CurrentCycleTime = await GetCurrentCycleTimeAsync();
                data.WorkpieceCount = await GetWorkpieceCountAsync();
                data.SpindleOverride = await GetSpindleOverrideAsync();
                data.FeedOverride = await GetFeedOverrideAsync();
                data.ActualSpindleSpeed = await GetActualSpindleSpeedAsync();
                data.ActualFeedRate = await GetActualFeedRateAsync();
                data.AlarmStatus = await GetAlarmStatusAsync();
                data.MachineStatus = await GetMachineStatusAsync();

                return data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取实时数据时发生错误");
                ErrorOccurred?.Invoke(this, $"数据获取错误: {ex.Message}");
                
                return new TNC640Data
                {
                    Timestamp = DateTime.Now,
                    IsConnected = _isConnected,
                    IsDataValid = false,
                    LastError = ex.Message
                };
            }
        }

        public Task StartDataCollectionAsync()
        {
            if (_isDataCollectionRunning)
            {
                _logger.LogWarning("数据采集已经在运行中");
                return Task.CompletedTask;
            }

            _logger.LogInformation("开始数据采集，间隔: {Interval}ms", _configuration.DataCollectionIntervalMs);

            _cancellationTokenSource = new CancellationTokenSource();
            _isDataCollectionRunning = true;

            _dataCollectionTimer = new Timer(async _ => await CollectDataAsync(),
                null, TimeSpan.Zero, TimeSpan.FromMilliseconds(_configuration.DataCollectionIntervalMs));

            return Task.CompletedTask;
        }

        public async Task StopDataCollectionAsync()
        {
            if (!_isDataCollectionRunning)
            {
                return;
            }

            _logger.LogInformation("停止数据采集");
            
            _isDataCollectionRunning = false;
            _cancellationTokenSource?.Cancel();
            
            if (_dataCollectionTimer != null)
            {
                await _dataCollectionTimer.DisposeAsync();
                _dataCollectionTimer = null;
            }
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                if (!_isConnected)
                {
                    return await ConnectAsync();
                }

                // 发送测试命令
                var testCommand = CreateLSV2Command("R", "SYS\\VERSION");
                await SendCommandAsync(testCommand);
                var response = await ReceiveResponseAsync();
                
                return !string.IsNullOrEmpty(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试连接时发生错误");
                return false;
            }
        }

        private async Task<bool> PerformLSV2HandshakeAsync()
        {
            try
            {
                // LSV2协议握手逻辑
                // 这里需要根据实际的LSV2协议规范实现
                _logger.LogDebug("执行LSV2协议握手");
                
                // 示例：发送登录命令（如果需要认证）
                if (_configuration.LSV2.RequireAuthentication)
                {
                    var loginCommand = CreateLoginCommand(_configuration.LSV2.Username, _configuration.LSV2.Password);
                    await SendCommandAsync(loginCommand);
                    var loginResponse = await ReceiveResponseAsync();
                    
                    if (string.IsNullOrEmpty(loginResponse) || loginResponse.Contains("ERROR"))
                    {
                        _logger.LogError("LSV2登录失败: {Response}", loginResponse);
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "LSV2握手过程中发生错误");
                return false;
            }
        }

        private async Task CollectDataAsync()
        {
            if (!_isConnected || _cancellationTokenSource?.Token.IsCancellationRequested == true)
            {
                return;
            }

            try
            {
                var data = await GetRealTimeDataAsync();
                DataUpdated?.Invoke(this, data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据采集过程中发生错误");
                ErrorOccurred?.Invoke(this, $"数据采集错误: {ex.Message}");
            }
        }

        // LSV2协议相关的私有方法将在下一部分实现
        private byte[] CreateLSV2Command(string command, string parameter)
        {
            // LSV2命令格式实现
            var commandString = $"{command} {parameter}\r\n";
            return Encoding.ASCII.GetBytes(commandString);
        }

        private byte[] CreateLoginCommand(string username, string password)
        {
            // LSV2登录命令实现
            var loginString = $"LOGIN {username} {password}\r\n";
            return Encoding.ASCII.GetBytes(loginString);
        }

        private async Task SendCommandAsync(byte[] command)
        {
            if (_networkStream == null)
                throw new InvalidOperationException("网络流未初始化");

            await _networkStream.WriteAsync(command, 0, command.Length);
            await _networkStream.FlushAsync();
        }

        private async Task<string> ReceiveResponseAsync()
        {
            if (_networkStream == null)
                throw new InvalidOperationException("网络流未初始化");

            var buffer = new byte[1024];
            var bytesRead = await _networkStream.ReadAsync(buffer, 0, buffer.Length);
            return Encoding.ASCII.GetString(buffer, 0, bytesRead).Trim();
        }

        // 具体数据获取方法实现
        private async Task<string> GetProgramFileNameAsync()
        {
            try
            {
                var command = CreateLSV2Command("R", "PGM\\ACT");
                await SendCommandAsync(command);
                var response = await ReceiveResponseAsync();
                return ParseStringResponse(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取程序文件名时发生错误");
                return string.Empty;
            }
        }

        private async Task<TimeSpan> GetCurrentCycleTimeAsync()
        {
            try
            {
                var command = CreateLSV2Command("R", "PGM\\CYCLETIME");
                await SendCommandAsync(command);
                var response = await ReceiveResponseAsync();
                var seconds = ParseDoubleResponse(response);
                return TimeSpan.FromSeconds(seconds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取循环时间时发生错误");
                return TimeSpan.Zero;
            }
        }

        private async Task<int> GetWorkpieceCountAsync()
        {
            try
            {
                var command = CreateLSV2Command("R", "PGM\\PARTS");
                await SendCommandAsync(command);
                var response = await ReceiveResponseAsync();
                return ParseIntResponse(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取工件计数时发生错误");
                return 0;
            }
        }

        private async Task<double> GetSpindleOverrideAsync()
        {
            try
            {
                var command = CreateLSV2Command("R", "CHANNEL\\SPINDLE\\OVERRIDE");
                await SendCommandAsync(command);
                var response = await ReceiveResponseAsync();
                return ParseDoubleResponse(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取主轴倍率时发生错误");
                return 0.0;
            }
        }

        private async Task<double> GetFeedOverrideAsync()
        {
            try
            {
                var command = CreateLSV2Command("R", "CHANNEL\\FEED\\OVERRIDE");
                await SendCommandAsync(command);
                var response = await ReceiveResponseAsync();
                return ParseDoubleResponse(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取进给倍率时发生错误");
                return 0.0;
            }
        }

        private async Task<double> GetActualSpindleSpeedAsync()
        {
            try
            {
                var command = CreateLSV2Command("R", "CHANNEL\\SPINDLE\\ACT");
                await SendCommandAsync(command);
                var response = await ReceiveResponseAsync();
                return ParseDoubleResponse(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取主轴实际速度时发生错误");
                return 0.0;
            }
        }

        private async Task<double> GetActualFeedRateAsync()
        {
            try
            {
                var command = CreateLSV2Command("R", "CHANNEL\\FEED\\ACT");
                await SendCommandAsync(command);
                var response = await ReceiveResponseAsync();
                return ParseDoubleResponse(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取进给实际速度时发生错误");
                return 0.0;
            }
        }

        private async Task<AlarmStatus> GetAlarmStatusAsync()
        {
            try
            {
                var alarmStatus = new AlarmStatus();

                // 获取报警数量
                var countCommand = CreateLSV2Command("R", "SYS\\ALARM\\COUNT");
                await SendCommandAsync(countCommand);
                var countResponse = await ReceiveResponseAsync();
                alarmStatus.AlarmCount = ParseIntResponse(countResponse);
                alarmStatus.HasAlarm = alarmStatus.AlarmCount > 0;

                // 如果有报警，获取报警详情
                if (alarmStatus.HasAlarm)
                {
                    for (int i = 0; i < Math.Min(alarmStatus.AlarmCount, 10); i++) // 最多获取10个报警
                    {
                        var alarmCommand = CreateLSV2Command("R", $"SYS\\ALARM\\{i}");
                        await SendCommandAsync(alarmCommand);
                        var alarmResponse = await ReceiveResponseAsync();

                        var alarmInfo = ParseAlarmResponse(alarmResponse);
                        if (alarmInfo != null)
                        {
                            alarmStatus.Alarms.Add(alarmInfo);
                        }
                    }
                }

                return alarmStatus;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取报警状态时发生错误");
                return new AlarmStatus();
            }
        }

        private async Task<MachineStatus> GetMachineStatusAsync()
        {
            try
            {
                var command = CreateLSV2Command("R", "SYS\\STATE");
                await SendCommandAsync(command);
                var response = await ReceiveResponseAsync();
                return ParseMachineStatusResponse(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备状态时发生错误");
                return MachineStatus.Unknown;
            }
        }

        // 响应解析方法
        private string ParseStringResponse(string response)
        {
            if (string.IsNullOrEmpty(response) || response.StartsWith("ERROR"))
                return string.Empty;

            // 移除LSV2协议的响应头和尾
            var parts = response.Split(' ');
            return parts.Length > 1 ? parts[1].Trim() : string.Empty;
        }

        private double ParseDoubleResponse(string response)
        {
            var stringValue = ParseStringResponse(response);
            return double.TryParse(stringValue, out var result) ? result : 0.0;
        }

        private int ParseIntResponse(string response)
        {
            var stringValue = ParseStringResponse(response);
            return int.TryParse(stringValue, out var result) ? result : 0;
        }

        private AlarmInfo? ParseAlarmResponse(string response)
        {
            try
            {
                if (string.IsNullOrEmpty(response) || response.StartsWith("ERROR"))
                    return null;

                // 解析报警信息格式：ALARM_NUMBER|TYPE|DESCRIPTION|SEVERITY
                var parts = response.Split('|');
                if (parts.Length >= 3)
                {
                    return new AlarmInfo
                    {
                        AlarmNumber = parts[0].Trim(),
                        Description = parts[2].Trim(),
                        AlarmType = ParseAlarmType(parts[1].Trim()),
                        Severity = parts.Length > 3 ? ParseAlarmSeverity(parts[3].Trim()) : AlarmSeverity.Warning,
                        OccurredTime = DateTime.Now
                    };
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private AlarmType ParseAlarmType(string typeString)
        {
            return typeString.ToUpper() switch
            {
                "SYSTEM" => AlarmType.System,
                "PROGRAM" => AlarmType.Program,
                "OPERATION" => AlarmType.Operation,
                "HARDWARE" => AlarmType.Hardware,
                "COMMUNICATION" => AlarmType.Communication,
                _ => AlarmType.System
            };
        }

        private AlarmSeverity ParseAlarmSeverity(string severityString)
        {
            return severityString.ToUpper() switch
            {
                "INFO" => AlarmSeverity.Info,
                "WARNING" => AlarmSeverity.Warning,
                "ERROR" => AlarmSeverity.Error,
                "CRITICAL" => AlarmSeverity.Critical,
                _ => AlarmSeverity.Warning
            };
        }

        private MachineStatus ParseMachineStatusResponse(string response)
        {
            var statusString = ParseStringResponse(response);
            return statusString.ToUpper() switch
            {
                "POWEROFF" => MachineStatus.PowerOff,
                "POWERON" => MachineStatus.PowerOn,
                "STANDBY" => MachineStatus.Standby,
                "RUNNING" => MachineStatus.Running,
                "PAUSED" => MachineStatus.Paused,
                "STOPPED" => MachineStatus.Stopped,
                "ALARM" => MachineStatus.Alarm,
                "MAINTENANCE" => MachineStatus.Maintenance,
                _ => MachineStatus.Unknown
            };
        }

        public void Dispose()
        {
            StopDataCollectionAsync().Wait();
            DisconnectAsync().Wait();
            _cancellationTokenSource?.Dispose();
        }
    }
}
