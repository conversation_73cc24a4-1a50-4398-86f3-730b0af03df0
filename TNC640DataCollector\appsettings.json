{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "TNC640": {"IpAddress": "*************", "Port": 19000, "ConnectionTimeoutMs": 5000, "ReadTimeoutMs": 3000, "DataCollectionIntervalMs": 1000, "ReconnectIntervalMs": 5000, "MaxReconnectAttempts": 10, "EnableAutoReconnect": true, "EnableDataValidation": true, "Protocol": "LSV2", "LSV2": {"Username": "", "Password": "", "RequireAuthentication": false, "CommandTimeoutMs": 2000, "KeepAliveIntervalMs": 30000}, "Logging": {"EnableCommunicationLog": false, "EnableDataLog": true, "LogLevel": "Information", "LogFilePath": "logs/tnc640.log"}}}